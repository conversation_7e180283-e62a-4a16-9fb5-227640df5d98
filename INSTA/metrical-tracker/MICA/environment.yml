name: MICA
channels:
  - pytorch
  - nvidia
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotlipy=0.7.0=py39h27cfd23_1003
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2022.10.11=h06a4308_0
  - certifi=2022.9.24=py39h06a4308_0
  - cffi=1.15.1=py39h5eee18b_2
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - cryptography=38.0.1=py39h9ce1e76_0
  - cuda=11.6.2=0
  - cuda-cccl=11.6.55=hf6102b2_0
  - cuda-command-line-tools=11.6.2=0
  - cuda-compiler=11.6.2=0
  - cuda-cudart=11.6.55=he381448_0
  - cuda-cudart-dev=11.6.55=h42ad0f4_0
  - cuda-cuobjdump=11.6.124=h2eeebcb_0
  - cuda-cupti=11.6.124=h86345e5_0
  - cuda-cuxxfilt=11.6.124=hecbf4f6_0
  - cuda-driver-dev=11.6.55=0
  - cuda-gdb=11.8.86=0
  - cuda-libraries=11.6.2=0
  - cuda-libraries-dev=11.6.2=0
  - cuda-memcheck=11.8.86=0
  - cuda-nsight=11.8.86=0
  - cuda-nsight-compute=11.8.0=0
  - cuda-nvcc=11.6.124=hbba6d2d_0
  - cuda-nvdisasm=11.8.86=0
  - cuda-nvml-dev=11.6.55=haa9ef22_0
  - cuda-nvprof=11.8.87=0
  - cuda-nvprune=11.6.124=he22ec0a_0
  - cuda-nvrtc=11.6.124=h020bade_0
  - cuda-nvrtc-dev=11.6.124=h249d397_0
  - cuda-nvtx=11.6.124=h0630a44_0
  - cuda-nvvp=11.8.87=0
  - cuda-runtime=11.6.2=0
  - cuda-samples=11.6.101=h8efea70_0
  - cuda-sanitizer-api=11.8.86=0
  - cuda-toolkit=11.6.2=0
  - cuda-tools=11.6.2=0
  - cuda-visual-tools=11.6.2=0
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.12.1=h4a9f257_0
  - gds-tools=1.4.0.31=0
  - giflib=5.2.1=h7b6447c_0
  - gmp=6.2.1=h295c915_3
  - gnutls=3.6.15=he1e5248_0
  - idna=3.4=py39h06a4308_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - jpeg=9e=h7f8727e_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libcublas=11.11.3.6=0
  - libcublas-dev=11.11.3.6=0
  - libcufft=10.9.0.58=0
  - libcufft-dev=10.9.0.58=0
  - libcufile=1.4.0.31=0
  - libcufile-dev=1.4.0.31=0
  - libcurand=10.3.0.86=0
  - libcurand-dev=10.3.0.86=0
  - libcusolver=11.4.1.48=0
  - libcusolver-dev=11.4.1.48=0
  - libcusparse=11.7.5.86=0
  - libcusparse-dev=11.7.5.86=0
  - libdeflate=1.8=h7f8727e_5
  - libffi=3.4.2=h6a678d5_6
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h7f8727e_2
  - libidn2=2.3.2=h7f8727e_0
  - libnpp=11.8.0.86=0
  - libnpp-dev=11.8.0.86=0
  - libnvjpeg=11.9.0.86=0
  - libnvjpeg-dev=11.9.0.86=0
  - libpng=1.6.37=hbc83047_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.4.0=hecacb30_2
  - libunistring=0.9.10=h27cfd23_0
  - libwebp=1.2.4=h11a3e52_0
  - libwebp-base=1.2.4=h5eee18b_0
  - lz4-c=1.9.3=h295c915_1
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py39h7f8727e_0
  - mkl_fft=1.3.1=py39hd3c417c_0
  - mkl_random=1.2.2=py39h51133e4_0
  - ncurses=6.3=h5eee18b_3
  - nettle=3.7.3=hbbd107a_1
  - nsight-compute=2022.3.0.22=0
  - numpy=1.23.4=py39h14f4228_0
  - numpy-base=1.23.4=py39h31eccc5_0
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1s=h7f8727e_0
  - pillow=9.2.0=py39hace64e9_1
  - pip=22.2.2=py39h06a4308_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pyopenssl=22.0.0=pyhd3eb1b0_0
  - pysocks=1.7.1=py39h06a4308_0
  - python=3.9.15=h7a1cb2a_2
  - pytorch=1.13.0=py3.9_cuda11.6_cudnn8.3.2_0
  - pytorch-cuda=11.6=h867d48c_0
  - pytorch-mutex=1.0=cuda
  - readline=8.2=h5eee18b_0
  - requests=2.28.1=py39h06a4308_0
  - setuptools=65.5.0=py39h06a4308_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.40.0=h5082296_0
  - tk=8.6.12=h1ccaba5_0
  - torchaudio=0.13.0=py39_cu116
  - torchvision=0.14.0=py39_cu116
  - typing_extensions=4.3.0=py39h06a4308_0
  - tzdata=2022f=h04d1e81_0
  - urllib3=1.26.12=py39h06a4308_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - xz=5.2.6=h5eee18b_0
  - zlib=1.2.13=h5eee18b_0
  - zstd=1.5.2=ha4553b6_0
  - pip:
      - albumentations==1.3.0
      - cachetools==5.2.0
      - chumpy==0.70
      - coloredlogs==15.0.1
      - contourpy==1.0.6
      - cycler==0.11.0
      - cython==0.29.32
      - easydict==1.10
      - face-alignment==1.3.5
      - falcon==3.1.1
      - falcon-multipart==0.2.0
      - flatbuffers==22.11.23
      - fonttools==4.38.0
      - google-api-core==2.11.0
      - google-api-python-client==2.69.0
      - google-auth==2.15.0
      - google-auth-httplib2==0.1.0
      - googleapis-common-protos==1.57.0
      - gunicorn==20.1.0
      - httplib2==0.21.0
      - humanfriendly==10.0
      - imageio==2.22.4
      - insightface==0.7
      - joblib==1.2.0
      - kiwisolver==1.4.4
      - llvmlite==0.39.1
      - loguru==0.6.0
      - matplotlib==3.6.2
      - mpmath==1.2.1
      - networkx==2.8.8
      - numba==0.56.4
      - oauth2client==4.1.3
      - onnx==1.13.0
      - onnxruntime==1.13.1
      - opencv-python==********
      - opencv-python-headless==********
      - packaging==21.3
      - prettytable==3.5.0
      - protobuf==3.20.2
      - pyasn1==0.4.8
      - pyasn1-modules==0.2.8
      - pydrive2==1.15.0
      - pyparsing==3.0.9
      - python-datauri==1.1.0
      - python-dateutil==2.8.2
      - pywavelets==1.4.1
      - pyyaml==6.0
      - qudida==0.0.4
      - rsa==4.9
      - scikit-image==0.19.3
      - scikit-learn==1.1.3
      - scipy==1.9.3
      - sympy==1.11.1
      - threadpoolctl==3.1.0
      - tifffile==2022.10.10
      - tqdm==4.64.1
      - trimesh==3.16.4
      - uritemplate==4.1.1
      - wcwidth==0.2.5
      - yacs==0.1.8
prefix: /home/<USER>/miniconda3/envs/MICA
