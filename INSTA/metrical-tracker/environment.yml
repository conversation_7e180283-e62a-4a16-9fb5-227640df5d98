name: tracker
channels:
  - pytorch3d
  - iopath
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotlipy=0.7.0=py39h27cfd23_1003
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2023.01.10=h06a4308_0
  - certifi=2022.12.7=py39h06a4308_0
  - cffi=1.15.1=py39h5eee18b_3
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - cryptography=39.0.1=py39h9ce1e76_0
  - cuda=11.6.1=0
  - cuda-cccl=11.6.55=hf6102b2_0
  - cuda-command-line-tools=11.6.2=0
  - cuda-compiler=11.6.2=0
  - cuda-cudart=11.6.55=he381448_0
  - cuda-cudart-dev=11.6.55=h42ad0f4_0
  - cuda-cuobjdump=11.6.124=h2eeebcb_0
  - cuda-cupti=11.6.124=h86345e5_0
  - cuda-cuxxfilt=11.6.124=hecbf4f6_0
  - cuda-driver-dev=11.6.55=0
  - cuda-gdb=12.1.55=0
  - cuda-libraries=11.6.1=0
  - cuda-libraries-dev=11.6.1=0
  - cuda-memcheck=11.8.86=0
  - cuda-nsight=12.1.55=0
  - cuda-nsight-compute=12.1.0=0
  - cuda-nvcc=11.6.124=hbba6d2d_0
  - cuda-nvdisasm=12.1.55=0
  - cuda-nvml-dev=11.6.55=haa9ef22_0
  - cuda-nvprof=12.1.55=0
  - cuda-nvprune=11.6.124=he22ec0a_0
  - cuda-nvrtc=11.6.124=h020bade_0
  - cuda-nvrtc-dev=11.6.124=h249d397_0
  - cuda-nvtx=11.6.124=h0630a44_0
  - cuda-nvvp=12.1.55=0
  - cuda-runtime=11.6.1=0
  - cuda-samples=11.6.101=h8efea70_0
  - cuda-sanitizer-api=12.1.55=0
  - cuda-toolkit=11.6.1=0
  - cuda-tools=11.6.1=0
  - cuda-visual-tools=11.6.1=0
  - flit-core=3.6.0=pyhd3eb1b0_0
  - freetype=2.12.1=h4a9f257_0
  - fvcore=0.1.5.post20221221=pyhd8ed1ab_0
  - gds-tools=1.6.0.25=0
  - giflib=5.2.1=h5eee18b_3
  - gmp=6.2.1=h295c915_3
  - gnutls=3.6.15=he1e5248_0
  - idna=3.4=py39h06a4308_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - iopath=0.1.9=py39
  - jpeg=9e=h5eee18b_1
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libcublas=11.9.2.110=h5e84587_0
  - libcublas-dev=11.9.2.110=h5c901ab_0
  - libcufft=10.7.1.112=hf425ae0_0
  - libcufft-dev=10.7.1.112=ha5ce4c0_0
  - libcufile=1.6.0.25=0
  - libcufile-dev=1.6.0.25=0
  - libcurand=10.3.2.56=0
  - libcurand-dev=10.3.2.56=0
  - libcusolver=11.3.4.124=h33c3c4e_0
  - libcusparse=11.7.2.124=h7538f96_0
  - libcusparse-dev=11.7.2.124=hbbe9722_0
  - libdeflate=1.17=h5eee18b_0
  - libffi=3.4.2=h6a678d5_6
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h7f8727e_2
  - libidn2=2.3.2=h7f8727e_0
  - libnpp=11.6.3.124=hd2722f0_0
  - libnpp-dev=11.6.3.124=h3c42840_0
  - libnvjpeg=11.6.2.124=hd473ad6_0
  - libnvjpeg-dev=11.6.2.124=hb5906b9_0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.5.0=h6a678d5_2
  - libunistring=0.9.10=h27cfd23_0
  - libwebp=1.2.4=h11a3e52_1
  - libwebp-base=1.2.4=h5eee18b_1
  - lz4-c=1.9.4=h6a678d5_0
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py39h7f8727e_0
  - mkl_fft=1.3.1=py39hd3c417c_0
  - mkl_random=1.2.2=py39h51133e4_0
  - ncurses=6.4=h6a678d5_0
  - nettle=3.7.3=hbbd107a_1
  - nsight-compute=2023.1.0.15=0
  - numpy=1.23.5=py39h14f4228_0
  - numpy-base=1.23.5=py39h31eccc5_0
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1t=h7f8727e_0
  - pillow=9.4.0=py39h6a678d5_0
  - pip=23.0.1=py39h06a4308_0
  - portalocker=2.7.0=py39hf3d152e_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pyopenssl=23.0.0=py39h06a4308_0
  - pysocks=1.7.1=py39h06a4308_0
  - python=3.9.16=h7a1cb2a_2
  - python_abi=3.9=2_cp39
  - pytorch=1.13.0=py3.9_cuda11.6_cudnn8.3.2_0
  - pytorch-cuda=11.6=h867d48c_1
  - pytorch-mutex=1.0=cuda
  - pytorch3d=0.7.2=py39_cu116_pyt1130
  - pyyaml=6.0=py39hb9d737c_4
  - readline=8.2=h5eee18b_0
  - requests=2.28.1=py39h06a4308_1
  - setuptools=65.6.3=py39h06a4308_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.41.1=h5eee18b_0
  - tabulate=0.9.0=pyhd8ed1ab_1
  - termcolor=2.2.0=pyhd8ed1ab_0
  - tk=8.6.12=h1ccaba5_0
  - torchvision=0.14.0=py39_cu116
  - tqdm=4.65.0=pyhd8ed1ab_1
  - typing_extensions=4.4.0=py39h06a4308_0
  - tzdata=2022g=h04d1e81_0
  - urllib3=1.26.14=py39h06a4308_0
  - wheel=0.38.4=py39h06a4308_0
  - xz=5.2.10=h5eee18b_1
  - yacs=0.1.8=pyhd8ed1ab_0
  - yaml=0.2.5=h7f98852_2
  - zlib=1.2.13=h5eee18b_0
  - zstd=1.5.2=ha4553b6_0
  - pip:
      - absl-py==1.4.0
      - attrs==22.2.0
      - cachetools==5.3.0
      - chumpy==0.70
      - contourpy==1.0.7
      - cycler==0.11.0
      - face-alignment==1.3.5
      - flatbuffers==23.3.3
      - fonttools==4.39.2
      - google-auth==2.16.2
      - google-auth-oauthlib==0.4.6
      - grpcio==1.51.3
      - imageio==2.26.1
      - importlib-metadata==6.1.0
      - importlib-resources==5.12.0
      - kiwisolver==1.4.4
      - lazy-loader==0.1
      - llvmlite==0.39.1
      - loguru==0.6.0
      - markdown==3.4.1
      - markupsafe==2.1.2
      - matplotlib==3.7.1
      # - mediapipe==0.10.20
      - networkx==3.0
      - numba==0.56.4
      - oauthlib==3.2.2
      - opencv-contrib-python==********
      - opencv-python==********
      - packaging==23.0
      - protobuf==3.20.3
      - pyasn1==0.4.8
      - pyasn1-modules==0.2.8
      - pyparsing==3.0.9
      - python-dateutil==2.8.2
      - pywavelets==1.4.1
      - requests-oauthlib==1.3.1
      - rsa==4.9
      - scikit-image==0.20.0
      - scipy==1.9.1
      - sounddevice==0.4.6
      - tensorboard==2.12.0
      - tensorboard-data-server==0.7.0
      - tensorboard-plugin-wit==1.8.1
      - tifffile==2023.3.15
      - trimesh==3.20.2
      - werkzeug==2.2.3
      - zipp==3.15.0
prefix: /home/<USER>/miniconda3/envs/tracker

